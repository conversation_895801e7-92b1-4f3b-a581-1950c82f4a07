/**
 * Enhanced Screen Component
 * 
 * Wrapper around blessed.screen with additional functionality
 * for better integration and management.
 */

import blessed from 'blessed';
import { EventEmitter } from 'events';
import { logDebug, logError } from '../../../utils/logger/log.js';
import { toNumber } from '../../../types/blessed-extensions.js';

export interface ScreenOptions extends blessed.Widgets.IScreenOptions {
  onResize?: (width: number, height: number) => void;
  onKeypress?: (ch: string, key: blessed.Widgets.Events.IKeyEventArg) => void;
  onExit?: () => void;
}

export class EnhancedScreen extends EventEmitter {
  private screen: blessed.Widgets.Screen;
  private options: ScreenOptions;
  private isDestroyed = false;

  constructor(options: ScreenOptions = {}) {
    super();
    
    this.options = options;
    
    // Create blessed screen with enhanced defaults
    this.screen = blessed.screen({
      smartCSR: true,
      title: 'Kritrima AI CLI',
      cursor: {
        artificial: true,
        shape: 'line',
        blink: true,
        color: 'white',
      },
      debug: process.env.DEBUG === '1',
      dockBorders: true,
      fullUnicode: true,
      autoPadding: true,
      warnings: false,
      ...options,
    });

    this.setupEventHandlers();
    this.setupGlobalKeyHandlers();
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    // Handle resize events
    this.screen.on('resize', () => {
      const width = toNumber(this.screen.width);
      const height = toNumber(this.screen.height);
      logDebug(`Screen resized: ${width}x${height}`);

      if (this.options.onResize) {
        this.options.onResize(width, height);
      }

      this.emit('resize', width, height);
    });

    // Handle keypress events
    this.screen.on('keypress', (ch, key) => {
      if (this.options.onKeypress) {
        this.options.onKeypress(ch, key);
      }
      
      this.emit('keypress', ch, key);
    });

    // Handle warnings
    this.screen.on('warning', (warning) => {
      logError('Screen warning:', new Error(warning));
    });

    // Handle element events
    this.screen.on('element click', (el, data) => {
      this.emit('elementClick', el, data);
    });

    this.screen.on('element focus', (el) => {
      this.emit('elementFocus', el);
    });

    this.screen.on('element blur', (el) => {
      this.emit('elementBlur', el);
    });
  }

  /**
   * Setup global key handlers
   */
  private setupGlobalKeyHandlers(): void {
    // Global exit handlers
    this.screen.key(['C-c'], () => {
      if (this.options.onExit) {
        this.options.onExit();
      } else {
        this.exit();
      }
    });

    // Debug key handler
    if (process.env.DEBUG === '1') {
      this.screen.key(['C-d'], () => {
        this.toggleDebug();
      });
    }
  }

  /**
   * Get the underlying blessed screen
   */
  getScreen(): blessed.Widgets.Screen {
    return this.screen;
  }

  /**
   * Render the screen
   */
  render(): void {
    if (!this.isDestroyed) {
      this.screen.render();
    }
  }

  /**
   * Clear the screen
   */
  clear(): void {
    this.screen.clearRegion(0, toNumber(this.screen.width), 0, toNumber(this.screen.height));
    this.render();
  }

  /**
   * Focus an element
   */
  focus(element: blessed.Widgets.Node): void {
    element.focus();
    this.render();
  }

  /**
   * Get screen dimensions
   */
  getDimensions(): { width: number; height: number } {
    return {
      width: this.screen.width,
      height: this.screen.height,
    };
  }

  /**
   * Set screen title
   */
  setTitle(title: string): void {
    this.screen.title = title;
  }

  /**
   * Toggle debug mode
   */
  private toggleDebug(): void {
    const debug = !this.screen.options.debug;
    this.screen.options.debug = debug;
    logDebug(`Screen debug mode: ${debug ? 'enabled' : 'disabled'}`);
  }

  /**
   * Add global key handler
   */
  key(keys: string | string[], callback: (ch?: string, key?: blessed.Widgets.Events.IKeyEventArg) => void): void {
    this.screen.key(keys, callback);
  }

  /**
   * Remove key handler
   */
  unkey(keys: string | string[], callback?: Function): void {
    this.screen.unkey(keys, callback);
  }

  /**
   * Save screen state
   */
  saveState(): void {
    this.screen.saveCursor();
  }

  /**
   * Restore screen state
   */
  restoreState(): void {
    this.screen.restoreCursor();
  }

  /**
   * Take screenshot (debug feature)
   */
  screenshot(): string {
    return this.screen.screenshot();
  }

  /**
   * Exit the application
   */
  exit(code: number = 0): void {
    this.destroy();
    process.exit(code);
  }

  /**
   * Destroy the screen
   */
  destroy(): void {
    if (this.isDestroyed) {
      return;
    }

    this.isDestroyed = true;
    
    try {
      this.screen.destroy();
      this.removeAllListeners();
    } catch (error) {
      logError('Error destroying screen:', error);
    }
  }

  /**
   * Check if screen is destroyed
   */
  get destroyed(): boolean {
    return this.isDestroyed;
  }

  /**
   * Get screen width
   */
  get width(): number {
    return this.screen.width;
  }

  /**
   * Get screen height
   */
  get height(): number {
    return this.screen.height;
  }

  /**
   * Get screen title
   */
  get title(): string {
    return this.screen.title;
  }

  /**
   * Proxy method calls to underlying screen
   */
  append(element: blessed.Widgets.Node): void {
    this.screen.append(element);
  }

  remove(element: blessed.Widgets.Node): void {
    this.screen.remove(element);
  }

  insert(element: blessed.Widgets.Node, index: number): void {
    this.screen.insert(element, index);
  }

  insertBefore(element: blessed.Widgets.Node, refElement: blessed.Widgets.Node): void {
    this.screen.insertBefore(element, refElement);
  }

  insertAfter(element: blessed.Widgets.Node, refElement: blessed.Widgets.Node): void {
    this.screen.insertAfter(element, refElement);
  }
}

export default EnhancedScreen;
