/**
 * Selection Indicator Component
 *
 * Provides visual indicators for selection states using Unicode symbols
 * with consistent display and accessibility support.
 */
import blessed from 'blessed';
export interface IndicatorOptions {
    parent: blessed.Widgets.Node;
    top?: string | number;
    left?: string | number;
    width?: string | number;
    height?: string | number;
    type?: 'checkbox' | 'radio' | 'arrow' | 'bullet' | 'custom';
    checked?: boolean;
    disabled?: boolean;
    style?: blessed.Widgets.IStyle;
    customSymbols?: {
        checked: string;
        unchecked: string;
        disabled: string;
    };
    label?: string;
    hidden?: boolean;
}
export declare const INDICATOR_SYMBOLS: {
    checkbox: {
        checked: string;
        unchecked: string;
        disabled: string;
    };
    radio: {
        checked: string;
        unchecked: string;
        disabled: string;
    };
    arrow: {
        checked: string;
        unchecked: string;
        disabled: string;
    };
    bullet: {
        checked: string;
        unchecked: string;
        disabled: string;
    };
    custom: {
        checked: string;
        unchecked: string;
        disabled: string;
    };
};
export declare class Indicator {
    private element;
    private options;
    private checked;
    private disabled;
    constructor(options: IndicatorOptions);
    private getSymbols;
    private getColor;
    private updateDisplay;
    isChecked(): boolean;
    setChecked(checked: boolean): void;
    toggle(): void;
    isDisabled(): boolean;
    setDisabled(disabled: boolean): void;
    setLabel(label: string): void;
    getLabel(): string | undefined;
    setType(type: IndicatorOptions['type']): void;
    getType(): IndicatorOptions['type'];
    setCustomSymbols(symbols: IndicatorOptions['customSymbols']): void;
    show(): void;
    hide(): void;
    focus(): void;
    blur(): void;
    destroy(): void;
    getElement(): blessed.Widgets.BoxElement;
    setPosition(top: string | number, left: string | number): void;
    setSize(width: string | number, height: string | number): void;
}
/**
 * Create a checkbox indicator
 */
export declare function createCheckbox(parent: blessed.Widgets.Node, options?: Partial<IndicatorOptions>): Indicator;
/**
 * Create a radio button indicator
 */
export declare function createRadio(parent: blessed.Widgets.Node, options?: Partial<IndicatorOptions>): Indicator;
/**
 * Create an arrow indicator
 */
export declare function createArrow(parent: blessed.Widgets.Node, options?: Partial<IndicatorOptions>): Indicator;
/**
 * Create a bullet indicator
 */
export declare function createBullet(parent: blessed.Widgets.Node, options?: Partial<IndicatorOptions>): Indicator;
/**
 * Create a group of radio indicators that work together
 */
export declare class RadioGroup {
    private indicators;
    private selectedIndex;
    constructor(parent: blessed.Widgets.Node, options: Array<{
        label: string;
        value?: string;
        disabled?: boolean;
    }>, groupOptions?: Partial<IndicatorOptions>);
    select(index: number): void;
    getSelected(): number;
    getSelectedIndicator(): Indicator | null;
    setDisabled(index: number, disabled: boolean): void;
    destroy(): void;
    getIndicators(): Indicator[];
}
/**
 * Create a group of checkbox indicators
 */
export declare class CheckboxGroup {
    private indicators;
    constructor(parent: blessed.Widgets.Node, options: Array<{
        label: string;
        value?: string;
        checked?: boolean;
        disabled?: boolean;
    }>, groupOptions?: Partial<IndicatorOptions>);
    getChecked(): number[];
    getCheckedIndicators(): Indicator[];
    setChecked(index: number, checked: boolean): void;
    setDisabled(index: number, disabled: boolean): void;
    checkAll(): void;
    uncheckAll(): void;
    destroy(): void;
    getIndicators(): Indicator[];
}
export default Indicator;
//# sourceMappingURL=indicator.d.ts.map