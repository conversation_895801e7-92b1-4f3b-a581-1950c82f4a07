/**
 * Enhanced List Component
 *
 * Wrapper around blessed.list with additional functionality
 * for better keyboard navigation and item management.
 */
import blessed from 'blessed';
import { EventEmitter } from 'events';
export interface ListItem {
    text: string;
    value?: any;
    data?: any;
    disabled?: boolean;
    separator?: boolean;
}
export interface EnhancedListOptions extends blessed.Widgets.ListOptions {
    items?: ListItem[] | string[];
    onSelect?: (item: ListItem | string, index: number) => void;
    onCancel?: () => void;
    onHighlight?: (item: ListItem | string, index: number) => void;
    searchable?: boolean;
    multiSelect?: boolean;
    emptyMessage?: string;
    loadingMessage?: string;
}
export declare class EnhancedList extends EventEmitter {
    private list;
    private options;
    private items;
    private filteredItems;
    private selectedIndices;
    private searchQuery;
    private isLoading;
    constructor(options: EnhancedListOptions);
    /**
     * Setup event handlers
     */
    private setupEventHandlers;
    /**
     * Setup search handlers
     */
    private setupSearchHandlers;
    /**
     * Setup multi-select handlers
     */
    private setupMultiSelectHandlers;
    /**
     * Get the underlying blessed list
     */
    getList(): blessed.Widgets.ListElement;
    /**
     * Set items
     */
    setItems(items: (ListItem | string)[]): void;
    /**
     * Add item
     */
    addItem(item: ListItem | string): void;
    /**
     * Remove item
     */
    removeItem(index: number): void;
    /**
     * Clear all items
     */
    clear(): void;
    /**
     * Get selected item
     */
    getSelected(): {
        item: ListItem | string;
        index: number;
    } | null;
    /**
     * Get all selected items (for multi-select)
     */
    getSelectedItems(): Array<{
        item: ListItem | string;
        index: number;
    }>;
    /**
     * Select item by index
     */
    select(index: number): void;
    /**
     * Focus the list
     */
    focus(): void;
    /**
     * Show loading state
     */
    showLoading(): void;
    /**
     * Hide loading state
     */
    hideLoading(): void;
    /**
     * Update display
     */
    private updateDisplay;
    /**
     * Get actual item index from filtered index
     */
    private getActualIndex;
    /**
     * Check if item is disabled
     */
    private isItemDisabled;
    /**
     * Toggle selection for multi-select
     */
    private toggleSelection;
    /**
     * Select all items
     */
    private selectAll;
    /**
     * Deselect all items
     */
    private deselectAll;
    /**
     * Page up
     */
    private pageUp;
    /**
     * Page down
     */
    private pageDown;
    /**
     * Select first item
     */
    private selectFirst;
    /**
     * Select last item
     */
    private selectLast;
    /**
     * Start search
     */
    private startSearch;
    /**
     * Search next
     */
    private searchNext;
    /**
     * Search previous
     */
    private searchPrevious;
    /**
     * Destroy the list
     */
    destroy(): void;
}
export default EnhancedList;
//# sourceMappingURL=list.d.ts.map