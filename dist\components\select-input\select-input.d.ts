/**
 * Select Input Component
 *
 * Provides a dropdown-style selection interface with keyboard navigation,
 * search functionality, and customizable rendering.
 */
import blessed from 'blessed';
import { EventEmitter } from 'events';
export interface SelectOption {
    value: string;
    label: string;
    description?: string;
    disabled?: boolean;
    group?: string;
}
export interface SelectInputOptions {
    parent: blessed.Widgets.Node;
    top?: string | number;
    left?: string | number;
    width?: string | number;
    height?: string | number;
    options: SelectOption[];
    placeholder?: string;
    searchable?: boolean;
    multiSelect?: boolean;
    maxHeight?: number;
    style?: blessed.Widgets.IStyle;
    border?: blessed.Widgets.IBorder;
    label?: string;
    hidden?: boolean;
    onSelect?: (option: SelectOption | SelectOption[]) => void;
    onChange?: (value: string | string[]) => void;
    onSearch?: (query: string) => void;
}
export declare class SelectInput extends EventEmitter {
    private container;
    private inputBox;
    private listBox;
    private options;
    private allOptions;
    private filteredOptions;
    private selectedOptions;
    private isOpen;
    private searchQuery;
    constructor(options: SelectInputOptions);
    private createContainer;
    private createInputBox;
    private createListBox;
    private setupEventHandlers;
    private handleInputKeyPress;
    private filterOptions;
    private updateInputDisplay;
    private updateListItems;
    private groupOptions;
    private selectCurrentItem;
    private selectOption;
    private open;
    private close;
    private render;
    getSelectedValue(): string | string[] | null;
    getSelectedOption(): SelectOption | SelectOption[] | null;
    setSelectedValue(value: string | string[]): void;
    setOptions(options: SelectOption[]): void;
    addOption(option: SelectOption): void;
    removeOption(value: string): void;
    clear(): void;
    focus(): void;
    blur(): void;
    show(): void;
    hide(): void;
    destroy(): void;
    getElement(): blessed.Widgets.BoxElement;
}
export default SelectInput;
//# sourceMappingURL=select-input.d.ts.map