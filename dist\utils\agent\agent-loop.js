/**
 * Agent Loop Core
 *
 * The heart of the AI system implementing sophisticated autonomous workflow
 * with tool calling, conversation state management, and streaming responses.
 */
import { EventEmitter } from 'events';
import { createOpenAIClient } from '../openai-client.js';
import { streamResponses, createResponse } from '../responses.js';
import { logInfo, logError, logDebug, createTimer } from '../logger/log.js';
import { handleExecCommand } from './handle-exec-command.js';
/**
 * Shell function tool definition
 */
const shellFunctionTool = {
    type: 'function',
    name: 'shell',
    description: 'Runs a shell command and returns its output. Use this to execute system commands, run scripts, or interact with the file system.',
    parameters: {
        type: 'object',
        properties: {
            command: {
                type: 'array',
                items: { type: 'string' },
                description: 'The command to run as an array of strings (command and arguments)',
            },
            workdir: {
                type: 'string',
                description: 'Working directory for the command (optional)',
            },
            timeout: {
                type: 'number',
                description: 'Timeout in milliseconds (optional, default: 30000)',
            },
        },
        required: ['command'],
    },
};
/**
 * Local shell function tool definition
 */
const localShellFunctionTool = {
    type: 'function',
    name: 'local_shell',
    description: 'Executes a shell command directly in the local environment. Use for quick commands and file operations.',
    parameters: {
        type: 'object',
        properties: {
            command: {
                type: 'array',
                items: { type: 'string' },
                description: 'The command to run as an array of strings',
            },
            workdir: {
                type: 'string',
                description: 'Working directory for the command (optional)',
            },
        },
        required: ['command'],
    },
};
/**
 * Agent Loop implementation
 */
export class AgentLoop extends EventEmitter {
    model;
    provider;
    approvalPolicy;
    client;
    transcript = [];
    pendingAborts = new Set();
    cumulativeThinkingMs = 0;
    maxIterations;
    timeout;
    debug;
    constructor(config) {
        super();
        this.model = config.model;
        this.provider = config.provider;
        this.approvalPolicy = config.approvalPolicy;
        this.maxIterations = config.maxIterations || 10;
        this.timeout = config.timeout || 30000;
        this.debug = config.debug || false;
        // Create OpenAI client
        this.client = createOpenAIClient({
            provider: this.provider,
            timeout: this.timeout,
        });
        logInfo(`Agent loop initialized: ${this.provider}/${this.model}`);
    }
    /**
     * Process user input through the agent loop
     */
    async processInput(input) {
        const timer = createTimer('agent-loop-process');
        try {
            logInfo('Processing user input through agent loop');
            // Add input to transcript
            this.transcript.push(input);
            // Build conversation context
            const messages = this.buildConversationContext();
            // Get available tools based on approval policy
            const tools = this.getAvailableTools();
            // Create AI request
            const requestInput = {
                messages,
                model: this.model,
                provider: this.provider,
                maxTokens: 4096,
                temperature: 0.7,
                tools: tools.length > 0 ? tools.map(tool => ({
                    type: 'function',
                    function: {
                        name: tool.name,
                        description: tool.description,
                        parameters: tool.parameters,
                    },
                })) : undefined,
                stream: true,
            };
            // Process through AI with streaming
            let response;
            if (requestInput.stream) {
                response = await this.processStreamingResponse(requestInput);
            }
            else {
                response = await createResponse(requestInput, this.client);
            }
            // Handle function calls if present
            if (this.hasFunctionCalls(response)) {
                response = await this.processFunctionCalls(response);
            }
            // Add response to transcript
            this.addResponseToTranscript(response);
            timer.end();
            return response;
        }
        catch (error) {
            timer.end();
            logError('Agent loop processing failed', error);
            throw error;
        }
    }
    /**
     * Process streaming response
     */
    async processStreamingResponse(requestInput) {
        try {
            const completion = await this.client.chat.completions.create({
                ...requestInput,
                tools: requestInput.tools,
                stream: true,
            });
            let finalResponse = null;
            // Process streaming events
            for await (const event of streamResponses(requestInput, completion)) {
                this.emit('response-event', event);
                if (event.type === 'response.completed') {
                    finalResponse = event.data.response;
                    this.cumulativeThinkingMs += event.data.duration;
                }
            }
            if (!finalResponse) {
                throw new Error('No response received from streaming');
            }
            return finalResponse;
        }
        catch (error) {
            logError('Streaming response processing failed', error);
            throw error;
        }
    }
    /**
     * Build conversation context from transcript
     */
    buildConversationContext() {
        return this.transcript.map(item => ({
            role: item.role,
            content: item.content.map(c => {
                if (c.type === 'input_text') {
                    return { type: 'text', text: c.text || '' };
                }
                else if (c.type === 'input_image' && c.image) {
                    return {
                        type: 'image_url',
                        image_url: { url: c.image.data || c.image.url || '' },
                    };
                }
                return { type: 'text', text: '' };
            }),
        }));
    }
    /**
     * Get available tools based on approval policy
     */
    getAvailableTools() {
        const tools = [];
        // Add shell tools based on approval policy
        if (this.approvalPolicy !== 'suggest') {
            tools.push(shellFunctionTool);
            tools.push(localShellFunctionTool);
        }
        return tools;
    }
    /**
     * Check if response has function calls
     */
    hasFunctionCalls(response) {
        return response.content.some(c => c.type === 'function_call');
    }
    /**
     * Process function calls in response
     */
    async processFunctionCalls(response) {
        const functionCalls = response.content.filter(c => c.type === 'function_call' && c.functionCall);
        if (functionCalls.length === 0) {
            return response;
        }
        logInfo(`Processing ${functionCalls.length} function calls`);
        // Process each function call
        for (const callContent of functionCalls) {
            if (callContent.functionCall) {
                await this.handleFunctionCall(callContent.functionCall);
            }
        }
        return response;
    }
    /**
     * Handle individual function call
     */
    async handleFunctionCall(functionCall) {
        const { name, arguments: argsString } = functionCall;
        try {
            logDebug(`Executing function call: ${name}`);
            const args = JSON.parse(argsString);
            switch (name) {
                case 'shell':
                case 'local_shell':
                    await this.handleShellCommand(args);
                    break;
                default:
                    logError(`Unknown function: ${name}`);
            }
        }
        catch (error) {
            logError(`Function call failed: ${name}`, error);
        }
    }
    /**
     * Handle shell command execution
     */
    async handleShellCommand(args) {
        try {
            const execInput = {
                command: args.command,
                workdir: args.workdir || process.cwd(),
                timeout: args.timeout || 30000,
            };
            // Execute command through handler
            const result = await handleExecCommand(execInput, {
                approvalPolicy: this.approvalPolicy,
                workingDirectory: execInput.workdir,
            });
            logInfo(`Shell command executed: ${execInput.command.join(' ')}`);
            logDebug(`Command result: ${result.success ? 'success' : 'failed'}`);
        }
        catch (error) {
            logError('Shell command execution failed', error);
        }
    }
    /**
     * Add response to transcript
     */
    addResponseToTranscript(response) {
        // Convert response to input item format for transcript
        const transcriptItem = {
            role: 'assistant',
            content: response.content.map(c => {
                if (c.type === 'output_text') {
                    return { type: 'input_text', text: c.text || '' };
                }
                return { type: 'input_text', text: '' };
            }),
            type: 'message',
            id: response.id,
            timestamp: response.timestamp,
        };
        this.transcript.push(transcriptItem);
    }
    /**
     * Update model configuration
     */
    updateModel(model, provider) {
        this.model = model;
        if (provider) {
            this.provider = provider;
            this.client = createOpenAIClient({
                provider: this.provider,
                timeout: this.timeout,
            });
        }
        logInfo(`Model updated: ${this.provider}/${this.model}`);
    }
    /**
     * Update approval policy
     */
    updateApprovalPolicy(policy) {
        this.approvalPolicy = policy;
        logInfo(`Approval policy updated: ${policy}`);
    }
    /**
     * Update configuration
     */
    updateConfig(config) {
        if (config.model) {
            this.model = config.model;
        }
        if (config.provider) {
            this.provider = config.provider;
            this.client = createOpenAIClient({
                provider: this.provider,
                timeout: this.timeout,
            });
        }
        if (config.approvalPolicy) {
            this.approvalPolicy = config.approvalPolicy;
        }
        if (config.maxIterations) {
            this.maxIterations = config.maxIterations;
        }
        if (config.timeout) {
            this.timeout = config.timeout;
        }
        if (config.debug !== undefined) {
            this.debug = config.debug;
        }
        logInfo(`Agent loop configuration updated: ${this.provider}/${this.model}`);
    }
    /**
     * Clear conversation transcript
     */
    clearTranscript() {
        this.transcript = [];
        logInfo('Conversation transcript cleared');
    }
    /**
     * Get conversation statistics
     */
    getStats() {
        return {
            messageCount: this.transcript.length,
            thinkingTime: this.cumulativeThinkingMs,
            modelInfo: {
                model: this.model,
                provider: this.provider,
            },
        };
    }
    /**
     * Abort pending operations
     */
    abort() {
        logInfo('Aborting agent loop operations');
        this.pendingAborts.clear();
        this.emit('abort');
    }
}
//# sourceMappingURL=agent-loop.js.map