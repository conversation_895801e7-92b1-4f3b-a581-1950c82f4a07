/**
 * Advanced Text Input Component
 *
 * Provides enhanced text input functionality with cursor management,
 * keyboard shortcuts, line continuation, text selection, and paste handling.
 */
import blessed from 'blessed';
import { EventEmitter } from 'events';
export interface TextInputOptions {
    parent: blessed.Widgets.Node;
    top?: string | number;
    left?: string | number;
    width?: string | number;
    height?: string | number;
    placeholder?: string;
    multiline?: boolean;
    maxLength?: number;
    style?: blessed.Widgets.IStyle;
    border?: blessed.Widgets.IBorder;
    label?: string;
    hidden?: boolean;
    password?: boolean;
    onSubmit?: (value: string) => void;
    onChange?: (value: string) => void;
    onKeyPress?: (ch: string, key: blessed.Widgets.IKeyEventArg) => void;
}
export declare class TextInput extends EventEmitter {
    private element;
    private options;
    private cursorPosition;
    private selectionStart;
    private selectionEnd;
    private history;
    private historyIndex;
    private clipboard;
    constructor(options: TextInputOptions);
    private setupEventHandlers;
    private handleKeyPress;
    private insertText;
    private handleBackspace;
    private handleDelete;
    private moveCursorLeft;
    private moveCursorRight;
    private moveToStart;
    private moveToEnd;
    private deleteToEnd;
    private deleteToStart;
    private deleteWord;
    private moveWordForward;
    private moveWordBackward;
    private hasSelection;
    private clearSelection;
    private deleteSelection;
    private selectLeft;
    private selectRight;
    private selectAll;
    private copy;
    private paste;
    private cut;
    private historyUp;
    private historyDown;
    private submit;
    private updateDisplay;
    private updatePlaceholder;
    getValue(): string;
    setValue(value: string): void;
    clear(): void;
    focus(): void;
    blur(): void;
    show(): void;
    hide(): void;
    destroy(): void;
    getElement(): blessed.Widgets.TextareaElement;
    setPlaceholder(placeholder: string): void;
    addToHistory(value: string): void;
    getHistory(): string[];
    clearHistory(): void;
}
export default TextInput;
//# sourceMappingURL=text-input.d.ts.map